import { But<PERSON> } from "@/components/ui/button";
import ProductDetailsModal from "./ProductDetailsModal";
import LotusMotif from "./ui/LotusMotif";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "Architect",
      company: "MUMBAI | INDIA",
      quote: "Lotus Marbles transformed our luxury residential project with their exquisite marble collection. The quality and finish exceeded our expectations, and their professional team ensured timely delivery.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&q=80",
      rating: 5
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      role: "Interior Designer",
      company: "DELHI | INDIA", 
      quote: "Working with Lotus Marbles has been a pleasure. Their diverse tile collection and expert guidance helped us create stunning spaces for our clients. Highly recommended!",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b08c?auto=format&fit=crop&w=150&q=80",
      rating: 5
    },
    {
      name: "<PERSON><PERSON>",
      role: "Managing Director",
      company: "MAHARASHTRA | INDIA",
      quote: "Excellent service by Lotus; we received fantastic rates, prompt delivery, far superior to nearby businesses. We purchased premium tiles for our commercial project with smooth processes.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80",
      rating: 5
    }
  ];

  const trendingProducts = [
    {
      name: "Wooden Look Tiles",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?auto=format&fit=crop&w=400&q=80",
      price: "₹45/sq ft",
      product: {
        title: "Premium Wooden Look Tiles",
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?auto=format&fit=crop&w=800&q=80",
        price: "₹45/sq ft",
        description: "Get the warmth and beauty of wood with the durability of ceramic. Our wooden look tiles provide the perfect solution for areas where real wood isn't practical.",
        features: [
          "Realistic wood grain texture",
          "Water and moisture resistant",
          "No maintenance required",
          "Scratch and dent resistant",
          "Available in multiple wood tones"
        ],
        specifications: {
          "Size": "200x1200mm",
          "Thickness": "10mm",
          "Finish": "Matt",
          "Water Absorption": "<0.5%",
          "Grade": "Premium"
        },
        rating: 5,
        reviews: 198
      }
    },
    {
      name: "Natural Stone", 
      image: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?auto=format&fit=crop&w=400&q=80",
      price: "₹85/sq ft",
      product: {
        title: "Natural Stone Collection",
        image: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?auto=format&fit=crop&w=800&q=80",
        price: "₹85/sq ft",
        description: "Authentic natural stone brings the beauty of nature into your home. Each piece is unique with its own character and natural variations.",
        features: [
          "100% natural stone",
          "Unique patterns and textures",
          "Heat resistant",
          "Environmentally friendly",
          "Increases property value"
        ],
        specifications: {
          "Material": "Natural Limestone",
          "Thickness": "15-18mm",
          "Finish": "Honed",
          "Origin": "India",
          "Grade": "Premium"
        },
        rating: 4,
        reviews: 143
      }
    },
    {
      name: "Premium Marble",
      image: "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?auto=format&fit=crop&w=400&q=80",
      price: "₹125/sq ft",
      product: {
        title: "Luxury Marble Collection",
        image: "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?auto=format&fit=crop&w=800&q=80",
        price: "₹125/sq ft",
        description: "Experience the ultimate in luxury with our premium marble collection. Sourced from the finest quarries, each slab tells a story of geological artistry.",
        features: [
          "Premium grade marble",
          "Unique veining patterns",
          "Polished to perfection",
          "Heat and scratch resistant",
          "Timeless elegance"
        ],
        specifications: {
          "Origin": "Italian Quarries",
          "Thickness": "18-20mm",
          "Finish": "Polished",
          "Hardness": "3-4 Mohs",
          "Grade": "A+"
        },
        rating: 5,
        reviews: 89
      }
    },
    {
      name: "Designer Tiles",
      image: "https://images.unsplash.com/photo-1615879218497-7242d47b07ce?auto=format&fit=crop&w=400&q=80",
      price: "₹65/sq ft",
      product: {
        title: "Contemporary Designer Tiles",
        image: "https://images.unsplash.com/photo-1615879218497-7242d47b07ce?auto=format&fit=crop&w=800&q=80",
        price: "₹65/sq ft",
        description: "Make a statement with our designer tile collection. Featuring bold patterns, contemporary colors, and innovative textures that transform any space into a work of art.",
        features: [
          "Contemporary geometric patterns",
          "Bold color combinations",
          "Innovative surface textures",
          "Easy to clean and maintain",
          "Perfect for accent walls"
        ],
        specifications: {
          "Size": "300x600mm",
          "Thickness": "9mm",
          "Finish": "Glossy/Matt",
          "Water Absorption": "<1%",
          "Grade": "Designer"
        },
        rating: 4,
        reviews: 167
      }
    }
  ];

  return (
    <>
      {/* Testimonials Section */}
      <section className="py-20 bg-white relative">
        <LotusMotif className="lotus-motif top-0 left-0 w-64 h-64" />
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="text-burgundy text-sm font-medium mb-2 tracking-wide uppercase">
              Client Reviews
            </div>
            <h2 className="font-serif-luxury text-4xl text-burgundy mb-4">
              What Our <span className="text-gold">Clients Say</span>
            </h2>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-cream rounded-lg p-8 relative">
                {/* Rating Stars */}
                <div className="flex justify-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-gold text-xl">★</span>
                  ))}
                </div>
                
                <p className="text-gray-700 mb-6 leading-relaxed text-center italic">
                  "{testimonial.quote}"
                </p>
                
                <div className="text-center">
                  <img 
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-gold mx-auto mb-4"
                  />
                  <h3 className="font-serif-luxury text-lg text-burgundy font-bold">
                    {testimonial.name}
                  </h3>
                  <p className="text-burgundy/70 font-medium text-sm">{testimonial.role}</p>
                  <p className="text-xs text-burgundy/50 tracking-wide">{testimonial.company}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Trending Products */}
      <section className="py-20 bg-burgundy">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="text-gold text-sm font-medium mb-2 tracking-wide uppercase">
              Popular Choices
            </div>
            <h2 className="font-serif-luxury text-4xl text-white mb-4">
              Trending <span className="text-gold">Products</span>
            </h2>
            <p className="text-white/80 max-w-2xl mx-auto">
              Discover our most popular tile and marble selections chosen by homeowners and designers across India.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {trendingProducts.map((product, index) => (
              <div key={index} className="group cursor-pointer">
                <div className="relative overflow-hidden rounded-lg border-2 border-transparent group-hover:border-gold transition-colors duration-300">
                  <img 
                    src={product.image}
                    alt={product.name}
                    className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent">
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <h3 className="font-serif-luxury text-xl font-bold mb-2">
                        {product.name}
                      </h3>
                      <div className="flex justify-between items-center">
                        <span className="text-gold font-semibold text-lg">{product.price}</span>
                        <ProductDetailsModal
                          product={product.product}
                          trigger={
                            <Button size="sm" className="bg-gold text-burgundy hover:bg-gold/90">
                              View Details
                            </Button>
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonials;
