@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 36 33% 96%; /* Creamy background */
    --foreground: 15 25% 20%; /* Deep maroon text */

    --card: 36 33% 98%;
    --card-foreground: 15 25% 20%;

    --popover: 36 33% 98%;
    --popover-foreground: 15 25% 20%;

    --primary: 10 60% 28%; /* Maroon */
    --primary-foreground: 43 100% 95%;

    --secondary: 43 30% 90%;
    --secondary-foreground: 10 60% 28%;

    --muted: 43 20% 95%;
    --muted-foreground: 15 10% 50%;

    --accent: 46 70% 60%; /* Gold */
    --accent-foreground: 10 60% 28%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 43 20% 85%;
    --input: 43 20% 85%;
    --ring: 10 60% 28%;

    --radius: 1.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .font-serif-luxury {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: 600;
    letter-spacing: 0.01em;
  }
  
  .text-burgundy {
    color: #8B4B47;
  }
  
  .bg-burgundy {
    background-color: #8B4B47;
  }
  
  .bg-cream {
    background-color: #F5F1EB;
  }
  
  .text-gold {
    color: #D4AF37;
  }
  
  .bg-gold {
    background-color: #D4AF37;
  }
  
  .border-gold {
    border-color: #D4AF37;
  }
  
  .border-cream {
    border-color: #F5F1EB;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(139, 75, 71, 0.15);
  }

  /* Glass effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .lotus-motif {
    position: absolute;
    opacity: 0.08;
    pointer-events: none;
    z-index: 0;
  }

  .section-box {
    background: #fff8ed;
    border-radius: 2rem 2rem 2rem 0;
    box-shadow: 0 8px 24px rgba(122, 59, 46, 0.08);
    padding: 2rem;
  }

  .luxury-btn {
    border: 1.5px solid #D4AF37;
    background: none;
    color: #8B4B47;
    border-radius: 2rem;
    padding: 0.75rem 2rem;
    font-weight: bold;
    transition: background 0.2s, color 0.2s;
  }

  .luxury-btn:hover {
    background: #8B4B47;
    color: #fff8ed;
    border-color: #8B4B47;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #8B4B47;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #D4AF37;
}
