import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Eye, Heart } from "lucide-react";
import ProductDetailsModal from "./ProductDetailsModal";
import LotusMotif from "./ui/LotusMotif";

const ProductsSection = () => {
  const categories = [
    {
      title: "Floor Tiles",
      icon: "🏠",
      description: "Durable and elegant flooring solutions",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?auto=format&fit=crop&w=400&q=80",
      price: "₹45/sq ft",
      popular: true,
      product: {
        title: "Premium Floor Tiles",
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?auto=format&fit=crop&w=800&q=80",
        price: "₹45/sq ft",
        description: "Our premium floor tiles combine durability with elegant design. Perfect for high-traffic areas, these tiles offer exceptional resistance to wear and staining while maintaining their beautiful appearance for years.",
        features: [
          "Anti-slip surface for safety",
          "Stain and scratch resistant",
          "Easy to clean and maintain",
          "Available in multiple sizes",
          "Suitable for indoor and outdoor use"
        ],
        specifications: {
          "Size": "600x600mm",
          "Thickness": "10mm",
          "Finish": "Matt/Glossy",
          "Water Absorption": "<0.5%",
          "Grade": "Premium"
        },
        rating: 5,
        reviews: 124
      }
    },
    {
      title: "Wall Tiles", 
      icon: "🧱",
      description: "Stylish wall coverings for every space",
      image: "https://images.unsplash.com/photo-1615879218497-7242d47b07ce?auto=format&fit=crop&w=400&q=80",
      price: "₹35/sq ft",
      product: {
        title: "Designer Wall Tiles",
        image: "https://images.unsplash.com/photo-1615879218497-7242d47b07ce?auto=format&fit=crop&w=800&q=80",
        price: "₹35/sq ft",
        description: "Transform your walls with our designer tile collection. These tiles feature contemporary patterns and colors that add character and style to any interior space.",
        features: [
          "Modern geometric patterns",
          "Fade-resistant colors",
          "Easy installation",
          "Moisture resistant",
          "Perfect for bathrooms and kitchens"
        ],
        specifications: {
          "Size": "300x600mm",
          "Thickness": "8mm",
          "Finish": "Glossy",
          "Water Absorption": "<3%",
          "Grade": "Standard"
        },
        rating: 4,
        reviews: 89
      }
    },
    {
      title: "Marble Slabs",
      icon: "💎",
      description: "Premium natural marble surfaces",
      image: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?auto=format&fit=crop&w=400&q=80",
      price: "₹125/sq ft",
      premium: true,
      product: {
        title: "Italian Carrara Marble",
        image: "https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?auto=format&fit=crop&w=800&q=80",
        price: "₹125/sq ft",
        description: "Authentic Italian Carrara marble brings timeless elegance to your space. Known for its distinctive white color and subtle gray veining, this premium natural stone is perfect for luxury applications.",
        features: [
          "100% natural Italian marble",
          "Unique veining patterns",
          "Heat resistant",
          "Polished finish",
          "Luxury grade quality"
        ],
        specifications: {
          "Origin": "Carrara, Italy",
          "Thickness": "18-20mm",
          "Finish": "Polished",
          "Density": "2.7 g/cm³",
          "Grade": "Premium A"
        },
        rating: 5,
        reviews: 67
      }
    },
    {
      title: "Outdoor Tiles",
      icon: "🌿",
      description: "Weather-resistant outdoor solutions",
      image: "https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?auto=format&fit=crop&w=400&q=80",
      price: "₹55/sq ft",
      product: {
        title: "Weather-Resistant Outdoor Tiles",
        image: "https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?auto=format&fit=crop&w=800&q=80",
        price: "₹55/sq ft",
        description: "Specially designed for outdoor applications, these tiles withstand harsh weather conditions while maintaining their beauty. Perfect for patios, balconies, and outdoor walkways.",
        features: [
          "Frost and freeze resistant",
          "UV stable colors",
          "Non-slip surface",
          "Low water absorption",
          "Suitable for pool areas"
        ],
        specifications: {
          "Size": "600x600mm",
          "Thickness": "12mm",
          "Finish": "Textured",
          "Water Absorption": "<0.1%",
          "Grade": "Exterior"
        },
        rating: 4,
        reviews: 156
      }
    }
  ];

  const features = [
    {
      title: "Bathroom Elegance",
      image: "https://images.unsplash.com/photo-1620626011761-996317b8d101?auto=format&fit=crop&w=800&q=80",
      description: "Luxury bathroom tile collections",
      rooms: "Bathrooms, Spa Areas"
    },
    {
      title: "Kitchen Perfection", 
      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?auto=format&fit=crop&w=800&q=80",
      description: "Modern kitchen tile designs",
      rooms: "Kitchens, Backsplashes"
    },
    {
      title: "Living Spaces",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?auto=format&fit=crop&w=800&q=80", 
      description: "Premium living room flooring",
      rooms: "Living Rooms, Halls"
    }
  ];

  const scrollToContact = () => {
    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="products" className="py-24 bg-gradient-to-br from-cream/70 to-white relative overflow-hidden">
      <LotusMotif className="lotus-motif top-0 right-0 w-64 h-64" />

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20 animate-fade-in">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full border border-gold/20 mb-4">
            <span className="text-burgundy text-sm font-medium tracking-wide uppercase">Our Product Range</span>
          </div>
          <h2 className="font-serif-luxury text-5xl lg:text-6xl text-burgundy mb-6">
            Explore Our Premium
            <br />
            <span className="text-gold relative">
              Tile Collections
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent rounded-full"></div>
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From classic elegance to contemporary designs, discover our extensive range of tiles and marble that perfectly suit your style and space requirements.
          </p>
        </div>

        {/* Product Categories Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-24">
          {categories.map((category, index) => (
            <div 
              key={index}
              className="group cursor-pointer relative"
            >
              {/* Badge */}
              {category.popular && (
                <div className="absolute -top-3 -right-3 bg-gold text-burgundy px-3 py-1 rounded-full text-xs font-bold z-20 shadow-lg">
                  Popular
                </div>
              )}
              {category.premium && (
                <div className="absolute -top-3 -right-3 bg-burgundy text-white px-3 py-1 rounded-full text-xs font-bold z-20 shadow-lg">
                  Premium
                </div>
              )}

              <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group-hover:-translate-y-2">
                <div className="relative overflow-hidden">
                  <img 
                    src={category.image}
                    alt={category.title}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute top-4 right-4 flex space-x-2">
                      <button className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                        <Eye className="w-5 h-5" />
                      </button>
                      <button className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                        <Heart className="w-5 h-5" />
                      </button>
                    </div>
                    
                    <div className="absolute bottom-4 left-4 right-4">
                      <ProductDetailsModal
                        product={category.product}
                        trigger={
                          <Button className="w-full bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white hover:text-burgundy transition-all duration-300">
                            View Details
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </Button>
                        }
                      />
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="absolute top-4 left-4 text-3xl bg-white/90 backdrop-blur-sm w-14 h-14 rounded-full flex items-center justify-center shadow-lg">
                    {category.icon}
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="font-serif-luxury text-xl font-bold text-burgundy mb-2">{category.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-gold font-bold text-lg">{category.price}</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Starting from</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Spaces */}
        <div className="text-center mb-16">
          <h2 className="font-serif-luxury text-4xl lg:text-5xl text-burgundy mb-4">
            Transform Every <span className="text-gold">Space</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            See how our premium tiles and marble can elevate different areas of your home
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 group-hover:-translate-y-1">
                <img 
                  src={feature.image}
                  alt={feature.title}
                  className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent">
                  <div className="absolute bottom-6 left-6 right-6 text-white">
                    <div className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-medium mb-3 inline-block">
                      {feature.rooms}
                    </div>
                    <h3 className="font-serif-luxury text-2xl font-bold mb-2">{feature.title}</h3>
                    <p className="text-white/90 mb-4">{feature.description}</p>
                    <Button 
                      size="sm" 
                      className="bg-gold hover:bg-gold/90 text-burgundy font-semibold"
                      onClick={scrollToContact}
                    >
                      Explore Ideas
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
