import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import LotusMotif from "./ui/LotusMotif";

const Footer = () => {
  return (
    <>
      {/* Newsletter Section */}
      <section className="bg-burgundy py-16 relative">
        <LotusMotif className="lotus-motif top-0 right-0 w-64 h-64" />
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <div className="bg-cream rounded-3xl p-8 relative">
              <h2 className="font-serif-luxury text-3xl text-burgundy mb-4">
                Stay With Us
              </h2>
              <div className="flex max-w-md mx-auto">
                <Input 
                  placeholder="Enter your email address..."
                  className="rounded-r-none border-burgundy"
                />
                <Button className="rounded-l-none bg-burgundy hover:bg-burgundy/90">
                  →
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Footer */}
      <footer className="bg-burgundy text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Quick Links */}
            <div>
              <h3 className="font-semibold text-lg mb-4 text-gold">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                <li><a href="#" className="hover:text-gold transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Catalogue</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">New Arrivals</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Events</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Expo</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Blogs</a></li>
              </ul>
            </div>

            {/* Varieties */}
            <div>
              <h3 className="font-semibold text-lg mb-4 text-gold">Varieties</h3>
              <ul className="space-y-2 text-sm">
                <li><a href="#" className="hover:text-gold transition-colors">Vitrified</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Wooden</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Marble</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Concrete</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Stone</a></li>
              </ul>
            </div>

            {/* Products */}
            <div>
              <h3 className="font-semibold text-lg mb-4 text-gold">Products</h3>
              <ul className="space-y-2 text-sm">
                <li><a href="#" className="hover:text-gold transition-colors">Floor Tiles</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Wall Tiles</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Bath Fitting</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Commercials</a></li>
                <li><a href="#" className="hover:text-gold transition-colors">Outdoor</a></li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="font-semibold text-lg mb-4 text-gold">Contact</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <p className="font-medium">Address</p>
                  <p className="text-white/80">Jetpur Rd, Opp. Mama CNG Station,</p>
                  <p className="text-white/80">At:Bela Village, Gujarat 363642</p>
                </div>
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-white/80"><EMAIL></p>
                  <p className="text-white/80"><EMAIL></p>
                </div>
                <div>
                  <p className="font-medium">Phone</p>
                  <p className="text-white/80">+91 9979404000</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-white/20 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              {/* Logo */}
              <div className="flex items-center space-x-2 mb-4 md:mb-0">
                <div className="w-8 h-8 bg-gold rounded-full flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-burgundy rounded-full"></div>
                </div>
                <div>
                  <div className="font-serif-luxury text-xl font-bold text-gold">LOTUS</div>
                  <div className="text-xs tracking-widest text-white/60">MARBLES</div>
                </div>
              </div>

              {/* Tagline */}
              <div className="text-center mb-4 md:mb-0">
                <p className="text-white/80">Best Tiles Company in India</p>
              </div>

              {/* Social Links */}
              <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-gold transition-colors">f</a>
                <a href="#" className="text-white/60 hover:text-gold transition-colors">t</a>
                <a href="#" className="text-white/60 hover:text-gold transition-colors">p</a>
                <a href="#" className="text-white/60 hover:text-gold transition-colors">@</a>
              </div>
            </div>

            <div className="text-center mt-6 pt-6 border-t border-white/10">
              <p className="text-white/60 text-sm">
                © 2024 Lotus Ceramic | All Right Reserved. | 
                <a href="#" className="hover:text-gold transition-colors ml-1">Terms & Condition</a> | 
                <a href="#" className="hover:text-gold transition-colors ml-1">Privacy Policy</a>
              </p>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
