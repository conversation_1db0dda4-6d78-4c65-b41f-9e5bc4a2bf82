import LotusMotif from "./ui/LotusMotif";

const ServicesSection = () => {
  const services = [
    {
      icon: "🏠",
      title: "Design Consultation",
      description: "Expert advice on tile selection and space planning to bring your vision to life.",
      features: ["3D Visualization", "Color Matching", "Space Planning", "Style Guidance"]
    },
    {
      icon: "🚚",
      title: "Installation Services",
      description: "Professional installation by certified craftsmen ensuring perfect results.",
      features: ["Skilled Craftsmen", "Quality Guarantee", "Timely Completion", "Post-Installation Care"]
    },
    {
      icon: "🛠️",
      title: "Custom Solutions",
      description: "Tailored tile and marble solutions for unique architectural requirements.",
      features: ["Custom Cutting", "Pattern Creation", "Size Variations", "Special Finishes"]
    },
    {
      icon: "💎",
      title: "Premium Support",
      description: "Comprehensive after-sales support and maintenance guidance.",
      features: ["Care Instructions", "Maintenance Tips", "Repair Services", "Warranty Support"]
    }
  ];

  return (
    <section className="py-20 bg-cream relative">
      <LotusMotif className="lotus-motif top-0 right-0 w-64 h-64" />
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="text-burgundy text-sm font-medium mb-2 tracking-wide uppercase">
            Our Services
          </div>
          <h2 className="font-serif-luxury text-4xl lg:text-5xl text-burgundy mb-6">
            Complete Tile & Marble
            <br />
            <span className="text-gold">Solutions</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            From consultation to installation, we provide end-to-end services to ensure your project is completed to perfection.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div 
              key={index}
              className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100"
            >
              <div className="text-4xl mb-6 text-center">{service.icon}</div>
              <h3 className="font-serif-luxury text-xl font-bold text-burgundy mb-4 text-center">
                {service.title}
              </h3>
              <p className="text-gray-600 text-center mb-6 leading-relaxed">
                {service.description}
              </p>
              <ul className="space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                    <div className="w-2 h-2 bg-gold rounded-full mr-3 flex-shrink-0"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
