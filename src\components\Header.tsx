import { Search, Home, Menu, Phone, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import SearchModal from "./SearchModal";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const navItems = [
    { label: "Home", href: "#home", icon: Home },
    { label: "About Us", href: "#about" },
    { label: "Products", href: "#products" },
    { label: "Services", href: "#services" },
    { label: "Contact", href: "#contact" },
  ];

  const scrollToSection = (href: string) => {
    const sectionId = href.replace('#', '');
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const handleGetQuote = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      {/* Top Bar */}
      {/* Removing the top bar as requested */}

      {/* Main Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white/95 backdrop-blur-sm border-b-2 border-gold shadow-lg' : 'bg-transparent'}`}>
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div 
              className="flex items-center space-x-3 group cursor-pointer"
              onClick={() => scrollToSection('#home')}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-burgundy to-burgundy/80 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                <div className="w-6 h-6 border-2 border-gold rounded-full group-hover:scale-110 transition-transform"></div>
              </div>
              <div className={scrolled ? "text-burgundy" : "text-burgundy"}>
                <div className="font-serif-luxury text-2xl font-bold group-hover:text-gold transition-colors">LOTUS</div>
                <div className="text-xs tracking-widest -mt-1 opacity-70">MARBLES</div>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navItems.map((item) => (
                <button
                  key={item.label}
                  onClick={() => scrollToSection(item.href)}
                  className={`hover:text-gold transition-all duration-300 font-medium text-sm flex items-center space-x-2 relative group py-2 ${scrolled ? 'text-burgundy' : 'text-burgundy'}`}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  <span>{item.label}</span>
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gold transition-all duration-300 group-hover:w-full"></div>
                </button>
              ))}
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
              <SearchModal
                trigger={
                  <Button variant="ghost" size="sm" className={`hover:text-gold hover:bg-gold/10 hidden sm:flex group ${scrolled ? 'text-burgundy' : 'text-burgundy'}`}>
                    <Search className="w-4 h-4 group-hover:scale-110 transition-transform" />
                  </Button>
                }
              />
              <Button 
                onClick={handleGetQuote}
                className="bg-burgundy hover:bg-burgundy/90 text-white hidden sm:flex shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                Get Quote
                <Phone className="w-4 h-4 ml-2 group-hover:rotate-12 transition-transform" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className={`lg:hidden hover:bg-burgundy/10 ${scrolled ? 'text-burgundy' : 'text-burgundy'}`}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <Menu className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden mt-4 pb-4 border-t border-cream animate-fade-in bg-white">
              <nav className="flex flex-col space-y-4 mt-4">
                {navItems.map((item) => (
                  <button
                    key={item.label}
                    onClick={() => scrollToSection(item.href)}
                    className="text-burgundy hover:text-gold transition-colors font-medium flex items-center space-x-3 py-2 hover:bg-cream/50 px-2 rounded text-left"
                  >
                    {item.icon && <item.icon className="w-4 h-4" />}
                    <span>{item.label}</span>
                  </button>
                ))}
                <div className="flex flex-col space-y-2 pt-4 border-t border-cream/50">
                  <SearchModal
                    trigger={
                      <Button variant="outline" size="sm" className="border-burgundy text-burgundy hover:bg-burgundy hover:text-white justify-start">
                        <Search className="w-4 h-4 mr-2" />
                        Search
                      </Button>
                    }
                  />
                  <Button 
                    onClick={handleGetQuote}
                    className="bg-burgundy hover:bg-burgundy/90 text-white justify-start"
                  >
                    <Phone className="w-4 h-4 mr-2" />
                    Get Quote
                  </Button>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;
