import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Play, Star } from "lucide-react";
import VideoModal from "./VideoModal";
import LotusMotif from "./ui/LotusMotif";

const Hero = () => {
  const scrollToProducts = () => {
    document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Placeholder for your texture image. 
  // Replace with a high-quality, seamless, light-colored marble or tile texture.
  // Example from Transparent Textures (good for subtle, tileable patterns):
  const textureImageUrl = "https://www.transparenttextures.com/patterns/light-marble.png"; 
  // Or find one on Unsplash, e.g., "https://images.unsplash.com/photo-1580792404939-783117227567?auto=format&fit=crop&w=1200&q=60" (this is a larger image, so bg-cover would be better)


  return (
    <section id="home" className="relative overflow-hidden min-h-screen"> {/* Removed direct bg-gradient */}
      
      {/* Layer 0: Subtle Texture Background */}
      <div
        className="absolute inset-0 z-0 bg-repeat opacity-[0.04]" // Low opacity for subtlety, bg-repeat if it's a pattern
        // If using a large, non-repeating image, change to: className="absolute inset-0 z-0 bg-cover bg-center opacity-[0.04]"
        style={{ backgroundImage: `url(${textureImageUrl})` }}
      ></div>

      {/* Layer 1: Gradient Overlay */}
      <div className="absolute inset-0 z-[1] bg-gradient-to-br from-cream/85 via-white/90 to-cream/85"></div>

      {/* Layer 2: Lotus Motif (Ensure it's positioned and has z-index) */}
      <LotusMotif className="lotus-motif absolute top-0 left-0 w-64 h-64 z-[2] opacity-60" /> {/* Added absolute, z-index, and opacity */}
      
      {/* Layer 3: Background Pattern Blurs (on top of gradient and texture) */}
      <div className="absolute inset-0 z-[3] opacity-5 pointer-events-none"> {/* Original opacity-5, now with z-index */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-burgundy rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 right-20 w-40 h-40 bg-gold rounded-full blur-3xl"></div>
      </div>

      {/* Layer 10: Main Hero Content */}
      <div className="container mx-auto px-4 py-20 relative z-10"> {/* Added relative z-10 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8 animate-fade-in">
            <div className="space-y-6">
              <div className="inline-flex items-center bg-cream/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gold/20">
                <Star className="w-4 h-4 text-gold mr-2 fill-current" />
                <span className="text-burgundy text-sm font-medium tracking-wide">Premium Tiles & Marble Collection</span>
              </div>
              
              <h1 className="font-serif-luxury text-5xl lg:text-7xl font-bold text-burgundy leading-tight">
                Transform Your
                <span className="text-gold block relative">
                  Luxury Space
                  <div className="absolute -bottom-2 left-0 w-32 h-1 bg-gradient-to-r from-gold to-transparent rounded-full"></div>
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed max-w-xl">
                Discover our exquisite collection of premium ceramic tiles, natural stone, and marble surfaces designed to elevate every corner of your home.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={scrollToProducts}
                className="bg-burgundy hover:bg-burgundy/90 text-white px-8 py-4 text-lg group transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Explore Collection
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <VideoModal
                trigger={
                  <Button 
                    variant="outline" 
                    className="border-2 border-burgundy text-burgundy hover:bg-burgundy hover:text-white px-8 py-4 text-lg group transition-all duration-300"
                  >
                    <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                    Watch Demo
                  </Button>
                }
                videoId="dQw4w9WgXcQ" // Reminder: This is a Rickroll link, consider changing it.
                title="Lotus Marbles Product Demo"
              />
            </div>

            {/* Enhanced Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200/50">
              {[
                { number: "500+", label: "Tile Designs", icon: "🏠" },
                { number: "50+", label: "Marble Varieties", icon: "💎" },
                { number: "1000+", label: "Happy Customers", icon: "⭐" }
              ].map((stat, index) => (
                <div key={index} className="text-center group cursor-pointer">
                  <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">{stat.icon}</div>
                  <div className="font-serif-luxury text-3xl font-bold text-burgundy group-hover:text-gold transition-colors">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Enhanced Image Grid */}
          <div className="relative mt-16 lg:mt-0">
            {/* Main showcase image */}
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl group"> {/* This z-10 is local to its parent */}
              <img 
                src="https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?auto=format&fit=crop&w=800&q=80"
                alt="Premium marble showcase"
                className="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                <div className="absolute bottom-6 left-6 text-white">
                  <div className="bg-gold/90 backdrop-blur-sm px-4 py-2 rounded-lg mb-3">
                    <span className="text-burgundy font-semibold">Featured Collection</span>
                  </div>
                  <h3 className="font-serif-luxury text-2xl font-bold mb-1">Italian Carrara Marble</h3>
                  <p className="text-white/90">Timeless elegance for modern homes</p>
                </div>
              </div>
            </div>

            {/* Floating elements - adjusted position */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-gold to-gold/70 rounded-2xl shadow-lg flex items-center justify-center z-20 animate-pulse"> {/* This z-20 is local */}
              <div className="text-center text-burgundy">
                <div className="font-serif-luxury text-2xl font-bold">25+</div>
                <div className="text-xs font-medium">Years</div>
              </div>
            </div>

            {/* Side images - repositioned to avoid text overlap and adjusted z-index */}
            <div className="absolute -bottom-20 -left-8 grid grid-cols-2 gap-4 z-0"> {/* This z-0 is local */}
              <img 
                src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?auto=format&fit=crop&w=200&q=80"
                alt="Designer tiles"
                className="w-24 h-24 rounded-xl object-cover shadow-lg hover:scale-110 transition-transform duration-300"
              />
              <img 
                src="https://images.unsplash.com/photo-1615873968403-89e068629265?auto=format&fit=crop&w=200&q=80"
                alt="Luxury bathroom"
                className="w-24 h-24 rounded-xl object-cover shadow-lg hover:scale-110 transition-transform duration-300 mt-6"
              />
            </div>

            {/* Background decoration */}
            <div className="absolute top-12 right-12 w-64 h-64 bg-gradient-to-br from-burgundy/10 to-transparent rounded-full blur-3xl"></div> {/* This has no z-index, will be behind positioned siblings with z-index */}
          </div>
        </div>
      </div>

      {/* Layer 20: Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20"> {/* Added z-20 */}
        <div className="w-6 h-10 border-2 border-burgundy/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-burgundy rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;