import { Button } from "@/components/ui/button";
import LotusMotif from "./ui/LotusMotif";

const AboutSection = () => {
  const achievements = [
    { number: "25+", label: "Years Experience" },
    { number: "10,000+", label: "Projects Completed" },
    { number: "500+", label: "Tile Varieties" },
    { number: "50+", label: "Premium Brands" }
  ];

  return (
    <section id="about" className="py-20 bg-cream relative">
      <LotusMotif className="lotus-motif bottom-0 left-0 w-64 h-64" />
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div>
              <div className="text-burgundy text-sm font-medium mb-2 tracking-wide uppercase">
                About Lotus Marbles
              </div>
              <h2 className="font-serif-luxury text-4xl lg:text-5xl text-burgundy mb-6">
                Crafting Beautiful Spaces 
                <span className="text-gold block">Since 1995</span>
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed mb-6">
                We are India's leading tiles and marble retailer, specializing in premium quality ceramic tiles, natural stone, and marble surfaces. Our commitment to excellence and customer satisfaction has made us the preferred choice for homeowners, architects, and interior designers across the country.
              </p>
              <p className="text-gray-600 leading-relaxed">
                From luxurious marble floors to designer wall tiles, we offer an extensive collection that combines traditional craftsmanship with modern innovation to transform your living and working spaces.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {achievements.map((achievement, index) => (
                <div key={index} className="text-center p-6 bg-cream rounded-lg">
                  <div className="font-serif-luxury text-3xl font-bold text-burgundy mb-2">
                    {achievement.number}
                  </div>
                  <div className="text-sm text-burgundy/70 font-medium">
                    {achievement.label}
                  </div>
                </div>
              ))}
            </div>

            <Button className="bg-burgundy hover:bg-burgundy/90 text-white px-8 py-3">
              Learn More About Us
            </Button>
          </div>

          {/* Right Content - Images */}
          <div className="relative">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-8">
                <img 
                  src="https://images.unsplash.com/photo-1600607686527-6fb886090705?auto=format&fit=crop&w=800&q=80"
                  alt="Luxury marble showroom"
                  className="rounded-lg shadow-xl w-full h-96 object-cover"
                />
              </div>
              <div className="col-span-4 space-y-4">
                <img 
                  src="https://images.unsplash.com/photo-1615873968403-89e068629265?auto=format&fit=crop&w=400&q=80"
                  alt="Premium tiles display"
                  className="rounded-lg shadow-lg w-full h-44 object-cover"
                />
                <img 
                  src="https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?auto=format&fit=crop&w=400&q=80"
                  alt="Marble collection"
                  className="rounded-lg shadow-lg w-full h-44 object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
